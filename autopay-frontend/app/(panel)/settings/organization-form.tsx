'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'
import { CreditCard, Globe, Palette, Search, Settings } from 'lucide-react'
import DomainsPage from '../domains/page'

const organizationFormSchema = z.object({
  domainConfig: z.object({
    brand_name: z.string().optional(),
    slogan: z.string().optional(),
    logo_url: z.string().url().optional().or(z.literal('')),
    favicon_url: z.string().url().optional().or(z.literal('')),
    theme_colors: z.object({
      primary: z.string(),
      secondary: z.string(),
      accent: z.string(),
      background: z.string(),
      surface: z.string(),
      text: z.string(),
      text_secondary: z.string(),
    }),
    meta_title: z.string().optional(),
    meta_description: z.string().optional(),
    meta_keywords: z.string().optional(),
    og_image_url: z.string().url().optional().or(z.literal('')),
  }),
  paymentInfo: z.object({
    bank_name: z.string().optional(),
    account_number: z.string().optional(),
    account_holder: z.string().optional(),
    branch: z.string().optional(),
    swift_code: z.string().optional(),
    tax_code: z.string().optional(),
    company_address: z.string().optional(),
  }),
})

type OrganizationFormValues = z.infer<typeof organizationFormSchema>

const defaultValues: Partial<OrganizationFormValues> = {
  domainConfig: {
    brand_name: '',
    slogan: '',
    logo_url: '',
    favicon_url: '',
    theme_colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      text_secondary: '#64748b',
    },
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    og_image_url: '',
  },
  paymentInfo: {
    bank_name: '',
    account_number: '',
    account_holder: '',
    branch: '',
    swift_code: '',
    tax_code: '',
    company_address: '',
  },
}

export function OrganizationForm() {
  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues,
    mode: 'onChange',
  })

  function onSubmit(data: OrganizationFormValues) {
    toast({
      title: 'Cập nhật cấu hình tổ chức thành công:',
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    })
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Cấu hình tổ chức</h2>
            <p className="text-muted-foreground text-sm">
              Quản lý thông tin thương hiệu và giao diện cho tổ chức của bạn
            </p>
          </div>

          <Tabs
            defaultValue="branding"
            className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="branding">
                <Palette className="mr-2 h-4 w-4" />
                Thương hiệu
              </TabsTrigger>
              <TabsTrigger value="theme">
                <Settings className="mr-2 h-4 w-4" />
                Giao diện
              </TabsTrigger>
              <TabsTrigger value="domains">
                <Globe className="mr-2 h-4 w-4" />
                Domains
              </TabsTrigger>
              <TabsTrigger value="payment">
                <CreditCard className="mr-2 h-4 w-4" />
                Thanh toán
              </TabsTrigger>
              <TabsTrigger value="advanced">
                <Search className="mr-2 h-4 w-4" />
                Nâng cao
              </TabsTrigger>
            </TabsList>

            <TabsContent
              value="branding"
              className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Thông tin thương hiệu</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="domainConfig.brand_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tên thương hiệu</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="AutoPAY"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="domainConfig.slogan"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Slogan</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Thanh toán tự động thông minh"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="domainConfig.logo_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL Logo</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://example.com/logo.png"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="domainConfig.favicon_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL Favicon</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://example.com/favicon.ico"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value="theme"
              className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Cấu hình màu sắc</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    {['primary', 'secondary', 'accent', 'background', 'surface', 'text', 'text_secondary'].map(
                      (colorKey) => (
                        <FormField
                          key={colorKey}
                          control={form.control}
                          name={`domainConfig.theme_colors.${colorKey}` as any}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="capitalize">{colorKey.replace('_', ' ')}</FormLabel>
                              <div className="flex gap-2">
                                <FormControl>
                                  <Input
                                    type="color"
                                    {...field}
                                    className="h-10 w-16"
                                  />
                                </FormControl>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder="#000000"
                                    className="flex-1"
                                  />
                                </FormControl>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value="domains"
              className="space-y-4">
              <DomainsPage />
            </TabsContent>

            <TabsContent
              value="payment"
              className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Thông tin thanh toán</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentInfo.bank_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tên ngân hàng</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Vietcombank"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="paymentInfo.account_number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Số tài khoản</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="**********"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentInfo.account_holder"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chủ tài khoản</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="CÔNG TY TNHH ABC"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="paymentInfo.branch"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chi nhánh</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Chi nhánh Hà Nội"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentInfo.swift_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Mã SWIFT</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="BFTVVNVX"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="paymentInfo.tax_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Mã số thuế</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="0123456789"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="paymentInfo.company_address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Địa chỉ công ty</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Số 123, Đường ABC, Phường XYZ, Quận DEF, Thành phố Hà Nội"
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value="advanced"
              className="space-y-4">
              {/* SEO Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">SEO</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="domainConfig.meta_title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Meta Title</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="AutoPAY - Thanh toán tự động"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.meta_description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Meta Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Mô tả cho SEO..."
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.meta_keywords"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Meta Keywords</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="thanh toán, tự động, autopay"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Button
            type="submit"
            size="sm">
            Cập nhật cấu hình
          </Button>
        </div>
      </form>
    </Form>
  )
}
